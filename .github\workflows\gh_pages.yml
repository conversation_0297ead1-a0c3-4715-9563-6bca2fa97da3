name: Build to gh_pages

on:
  pull_request:
    types:
      - closed
  workflow_dispatch:

jobs:
  changes:
    if: |
      github.event.pull_request.merged == true && 
      github.event.pull_request.base.ref == 'GUI'
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    outputs:
      docs: ${{ steps.filter.outputs.docs }}
    steps:
    - uses: dorny/paths-filter@v3
      id: filter
      with:
        filters: |
          docs:
            - 'docs/**'

  pages-deploy:
    needs: changes
    if: ${{ needs.changes.outputs.docs == 'true' }}
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        run: |
          cd docs && npm install
      - name: Build VitePress docs
        run: |
          cd docs && npm run docs:build
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: docs/.vitepress/dist
