---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "CGS"
  text: "Easily dl Comic"
  tagline: have fun it
  image:
    src: /CGS-girl.png
    alt: CGS
  actions:
    - theme: brand
      text: Quick-start
      link: /locate/en/deploy/quick-start
    - theme: alt
      text: Config
      link: /locate/en/config
---

<table><tbody>  
  <tr>
    <td><div align="center"><a href="https://www.2025copy.com/" target="_blank">
      <img src="../../assets/img/icons/website/copy.png" alt="logo" style="max-height: 80px">
      </a></div></td>
    <td><div align="center"><a href="https://mangabz.com" target="_blank">
      <img src="../../assets/img/icons/website/mangabz.png" alt="logo" style="max-height: 80px">
      </a></div></td>
    <td><div align="center"><a href="https://18comic.vip/" target="_blank">
      <img src="../../assets/img/icons/website/jm.png" alt="logo" style="max-height: 80px">
      </a></div></td>
    <td><div align="center"><a href="https://www.wnacg.com/" target="_blank">
      <img src="../../assets/img/icons/website/wnacg.png" alt="logo" style="max-height: 80px">
      </a></div></td>
    <td><div align="center"><a href="https://exhentai.org/" target="_blank">
      <img src="../../assets/img/icons/website/ehentai.png" alt="logo" style="max-height: 80px">
      </a></div></td>
    <td><div align="center"><a href="https://hitomi.la/" target="_blank">
      <img src="../../assets/img/icons/website/hitomi.png" alt="logo" style="max-height: 80px">
      </a></div></td>
  </tr>
  <tr>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_kaobei.json"></td>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_mangabz.json"></td>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_jm.json"></td>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_wnacg.json"></td>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_ehentai.json"></td>
    <td><img src="https://img.shields.io/endpoint?url=https://cgs-status-badges.pages.dev/status_hitomi.json"></td>
  </tr>
</tbody></table>

<table><tbody>
  <tr>
    <td><img src="https://raw.githubusercontent.com/jasoneri/imgur/main/CGS/common-usage.gif"></td>
    <td><img src="https://raw.githubusercontent.com/jasoneri/imgur/main/CGS/load_clip.gif"></td>
  </tr>  
</tbody></table>

## Functional Description

- Easy to use with simple configuration
- Just click a few times after preview to download, and you can also browse the website on the preview window
- Convenient to specify selection with input rules like `-3` (select the last three), `0` (select all), etc.
- Based on page retention, flipping pages is like putting items in a shopping cart
- Built-in restart is very convenient, and it is even more convenient with multiple launches
- Read clipboard stream
- De-duplication, add identifiers, etc.

### Credits

Thanks to 
- [PyStand](https://github.com/skywind3000/PyStand) / [Platypus](https://github.com/sveinbjornt/Platypus) for providing win/macOS packaging.
- [Ditto](https://github.com/sabrogden/Ditto) / [Maccy](https://github.com/p0deje/Maccy) for providing great win/macOS Clipboard Soft.
- [PyQt-Fluent-Widgets](https://github.com/zhiyiYo/PyQt-Fluent-Widgets/) for providing elegant qfluent ui.
- [VitePress](https://vitepress.dev) for providing a great documentation framework.
- [astral-sh/uv](https://github.com/astral-sh/uv) for providing a great requirements manager.
- Every comic production team / translator team / fans.

## contribution

Welcome to provide ISSUE or PR

<a href="https://github.com/jasoneri/ComicGUISpider/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=jasoneri/ComicGUISpider" />
</a>

## Disclaimer

- **Please do not** use ComicGUISpider for commercial purposes.
- **Please do not** make ComicGUISpider into video content and disseminate it on domestic video websites (copyright holders).
- **Please do not** use ComicGUISpider for any behavior that violates laws and regulations.

## Licence

[MIT licence](https://github.com/jasoneri/ComicGUISpider/blob/GUI/LICENSE)

---

![CGS_en](https://count.getloli.com/get/@CGS_en?theme=rule34)
