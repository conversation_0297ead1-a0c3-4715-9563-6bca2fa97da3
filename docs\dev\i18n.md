# 🌏 i18n guide

借助 [Weblate](https://hosted.weblate.org/engage/comicguispider/) 托管多语言的翻译  

Translation hosting by [Weblate](https://hosted.weblate.org/engage/comicguispider/)

## Development

检查当前语言环境 / Check lang env

```shell
python3 -c "from PyQt5.QtCore import QLocale;print(QLocale.system().name())"
```

::: tip
没找到对应语言编码的翻译文件时会默认使用 `en_US`  
If not yml file of your lang exists, default `en_US` will be used
:::

### Ui

::: warning
增加新翻译时请优先翻译含%s的字符串，否则程序会出错  
Please give priority to translating the string containing %s When new translation, otherwise the program will occur error
:::

翻译仅需处理单个 yml 文件如 [`en_US.yml`](https://github.com/jasoneri/ComicGUISpider/blob/GUI//assets/res/locale/en_US.yml)  

Translation only needs to handle single yaml file such as [`en_US.yml`](https://github.com/jasoneri/ComicGUISpider/blob/GUI//assets/res/locale/en_US.yml)  

### Documentation

文档皆存放在 `docs` 目录里，经由 Github-Action 做成 `github pages`  
参考英文的存储路径为 `docs/locate/en/*`

documents are stored in the `docs` directory, which will be made into `github pages` by Github-Action  
Reference English storage path is `docs/locate/en/*`

## Usage

### crawler

对适用区域为🌏的网站发出的请求会基于 `Vars.ua_accept_language`

Websites which applicable to 🌏 requests base on `Vars.ua_accept_language`

## Contant us

通过 [`issue`](https://github.com/jasoneri/ComicGUISpider/issues/new?template=feature-request.yml&labels=i18n) 进行反馈

feedback by [`issue`](https://github.com/jasoneri/ComicGUISpider/issues/new?template=feature-request.yml&labels=i18n)
