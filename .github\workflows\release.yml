name: Release
permissions:
  contents: write
  pull-requests: write
on:
  push:
    tags:
      - 'v*.*.*'  
  workflow_dispatch:

jobs:
  prebuild-windows:
    runs-on: windows-latest
    defaults:
      run:
        shell: pwsh
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 1
    - name: Add PATH
      run: |
        $7zPath = "C:\Program Files\7-Zip"
        $PresetPath = "D:\build\CGS_preset"
        Add-Content $env:GITHUB_PATH "$7zPath"
    - name: Download and Extract Preset
      run: |
        New-Item -ItemType Directory -Path D:\tmp -Force
        New-Item -ItemType Directory -Path D:\build -Force
        Invoke-WebRequest -Uri "https://github.com/jasoneri/imgur/releases/download/preset/CGS_preset.7z" -OutFile D:\tmp\CGS_preset.7z
    - name: Upload Preset
      uses: actions/upload-artifact@v4
      with:
        name: windows-preset
        path: D:\tmp\CGS_preset.7z

  build:
    runs-on: ubuntu-latest
    needs: [prebuild-windows]
    container:
      image: python:3.12
      volumes:
        - /tmp/build:/build

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 1
        path: src

    - name: Install dependencies
      run: python -m pip install pydos2unix py7zr tqdm loguru markdown pyyaml polib

    - name: Clean repository
      run: |
        mkdir -p /build/scripts
        mv src/* /build/scripts/
        rm -rf /build/scripts/.git
        find /build/scripts -name '__pycache__' -exec rm -rf {} +
        find /build/scripts -name '*.pyc' -delete

    - name: Download Windows Preset
      uses: actions/download-artifact@v4
      with:
        name: windows-preset
        path: /tmp/

    - name: Download macOS Preset
      run: |
        wget -O /tmp/CGS-macOS_preset.7z \
        https://github.com/jasoneri/imgur/releases/download/preset/CGS-macOS_preset.7z

    - name: Compose Release Notes
      id: compose_notes
      run: |
        TAG_NAME="${GITHUB_REF#refs/tags/}"
        echo "version: $TAG_NAME"
        base=$(cat /build/scripts/docs/_github/release_notes.md)

        case "$TAG_NAME" in
          *beta*)
            extra=$(cat /build/scripts/docs/_github/preset_preview.md)
            echo "is_beta=true" >> $GITHUB_OUTPUT
            ;;
          *)
            extra=$(cat /build/scripts/docs/_github/preset_stable.md)
            echo "is_beta=false" >> $GITHUB_OUTPUT
            ;;
        esac

        echo "$base\n$extra" > /build/full_body.md

    - name: Build packages
      working-directory: /build
      run: |
        python scripts/deploy/packer.py windows -v "${{ github.ref_name }}"
        python scripts/deploy/packer.py mac -v "${{ github.ref_name }}"

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ github.ref }}
        name: ${{ github.ref_name }}
        prerelease: ${{ steps.compose_notes.outputs.is_beta == 'true' }}
        body_path: /build/full_body.md
        files: |
          /build/CGS.7z
          /build/CGS-macOS.7z
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    