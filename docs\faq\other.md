## 1. 去重，增加标识相关说明

### 样例

1. http://jm-comic1.html “满开开花”
2. http://jm-comic2.html “满开开花”
3. http://wnacg-comic1.html “满开开花”

> [!Info] 举例：其中 comic1 和 comic2 是 jm 的两个作品id，第三条 comic1 是 wnacg 的作品id

#### 场景-原始

由于1和2同名，所以1下载后会被2覆盖，因为目录路径一样，3同理

#### 场景-去重✅

选择1后得 md5('jm'+'comic1')=md5_1，查表 md5_1 不存在，下载，产生目录`储存目录.../满开开花`  
再次下载1时查表发现 md5_1 已存在，不下载  
选择2后得 md5('jm'+'comic2')=md5_2，查表 md5_2 不存在，下载，记录进表并将内容覆盖到`储存目录.../满开开花`  
选择3后得 md5('wnacg'+'comic1')=md5_3，查表 md5_3 不存在，下载，记录进表并将内容覆盖到`储存目录.../满开开花`

#### 场景-增加标识❌

无论去重还是不去重，目录存在就覆盖

#### 场景-增加标识✅

将 spider_name 加唯一作品id加进命名尾部，例如下载上述三个得

+ `储存目录.../满开开花[jm-comic1]`
+ `储存目录.../满开开花[jm-comic2]`
+ `储存目录.../满开开花[wnacg-comic1]`

---------

### 其他

#### 1. id实则自定义

comic1 等 id 仅为示例，实际基于开发自定义  
例如 md5('kaobei'+福利莲+第一话)=id 就可去做常规漫的去重，常规漫的任务细化就是此 id

#### 2. 网站将同一内容的作品从 url 转移到 url2

考虑此情况实则并不常见，这种下重了也没所谓，少数情况
