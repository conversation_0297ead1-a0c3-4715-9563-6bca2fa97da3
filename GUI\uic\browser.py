# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'D:\code\ComicGUISpider\GUI\uic\browser.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets
from qfluentwidgets import CheckBox, ComboBox, CompactSpinBox, LineEdit, PrimaryToolButton, TextBrowser, TextEdit, TransparentToggleToolButton, TransparentToolButton


class Ui_browser(object):
    def setupUi(self, browser):
        browser.setObjectName("browser")
        browser.resize(1040, 417)
        browser.setMinimumSize(QtCore.QSize(1040, 417))
        browser.setMaximumSize(QtCore.QSize(1375, 16777215))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/CGS-logo.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        browser.setWindowIcon(icon)
        self.centralwidget = QtWidgets.QWidget(browser)
        self.centralwidget.setMinimumSize(QtCore.QSize(0, 0))
        self.centralwidget.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.centralwidget.setObjectName("centralwidget")
        self.gridLayout = QtWidgets.QGridLayout(self.centralwidget)
        self.gridLayout.setContentsMargins(-1, 0, -1, 5)
        self.gridLayout.setVerticalSpacing(2)
        self.gridLayout.setObjectName("gridLayout")
        self.groupBox = QtWidgets.QGroupBox(self.centralwidget)
        self.groupBox.setMaximumSize(QtCore.QSize(16777215, 40))
        self.groupBox.setTitle("")
        self.groupBox.setObjectName("groupBox")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.groupBox)
        self.horizontalLayout_2.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.topHintBox = TransparentToggleToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.topHintBox.sizePolicy().hasHeightForWidth())
        self.topHintBox.setSizePolicy(sizePolicy)
        self.topHintBox.setChecked(False)
        self.topHintBox.setObjectName("topHintBox")
        self.horizontalLayout_2.addWidget(self.topHintBox)
        self.line = QtWidgets.QFrame(self.groupBox)
        self.line.setFrameShape(QtWidgets.QFrame.VLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.horizontalLayout_2.addWidget(self.line)
        self.homeBtn = TransparentToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.homeBtn.sizePolicy().hasHeightForWidth())
        self.homeBtn.setSizePolicy(sizePolicy)
        self.homeBtn.setMaximumSize(QtCore.QSize(25, 16777215))
        self.homeBtn.setObjectName("homeBtn")
        self.horizontalLayout_2.addWidget(self.homeBtn)
        self.backBtn = TransparentToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.backBtn.sizePolicy().hasHeightForWidth())
        self.backBtn.setSizePolicy(sizePolicy)
        self.backBtn.setMaximumSize(QtCore.QSize(25, 16777215))
        self.backBtn.setObjectName("backBtn")
        self.horizontalLayout_2.addWidget(self.backBtn)
        self.forwardBtn = TransparentToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.forwardBtn.sizePolicy().hasHeightForWidth())
        self.forwardBtn.setSizePolicy(sizePolicy)
        self.forwardBtn.setMaximumSize(QtCore.QSize(25, 16777215))
        self.forwardBtn.setObjectName("forwardBtn")
        self.horizontalLayout_2.addWidget(self.forwardBtn)
        self.refreshBtn = TransparentToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.refreshBtn.sizePolicy().hasHeightForWidth())
        self.refreshBtn.setSizePolicy(sizePolicy)
        self.refreshBtn.setMaximumSize(QtCore.QSize(25, 16777215))
        self.refreshBtn.setObjectName("refreshBtn")
        self.horizontalLayout_2.addWidget(self.refreshBtn)
        self.addressEdit = LineEdit(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.addressEdit.sizePolicy().hasHeightForWidth())
        self.addressEdit.setSizePolicy(sizePolicy)
        self.addressEdit.setMinimumSize(QtCore.QSize(500, 0))
        self.addressEdit.setObjectName("addressEdit")
        self.horizontalLayout_2.addWidget(self.addressEdit)
        spacerItem = QtWidgets.QSpacerItem(632, 15, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.copyBtn = TransparentToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.copyBtn.sizePolicy().hasHeightForWidth())
        self.copyBtn.setSizePolicy(sizePolicy)
        self.copyBtn.setObjectName("copyBtn")
        self.horizontalLayout_2.addWidget(self.copyBtn)
        self.ensureBtn = PrimaryToolButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.ensureBtn.sizePolicy().hasHeightForWidth())
        self.ensureBtn.setSizePolicy(sizePolicy)
        self.ensureBtn.setMinimumSize(QtCore.QSize(40, 0))
        self.ensureBtn.setChecked(False)
        self.ensureBtn.setObjectName("ensureBtn")
        self.horizontalLayout_2.addWidget(self.ensureBtn)
        self.gridLayout.addWidget(self.groupBox, 0, 0, 1, 1)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.gridLayout.addLayout(self.horizontalLayout, 1, 0, 1, 1)
        browser.setCentralWidget(self.centralwidget)

        self.retranslateUi(browser)
        QtCore.QMetaObject.connectSlotsByName(browser)

    def retranslateUi(self, browser):
        _translate = QtCore.QCoreApplication.translate
        browser.setWindowTitle(_translate("browser", "inner browser/内置浏览器"))
        self.topHintBox.setToolTip(_translate("browser", "WindowStaysOnTopHint/窗口置顶"))
        self.homeBtn.setToolTip(_translate("browser", "home/回到初始页"))
        self.backBtn.setToolTip(_translate("browser", "back/后退"))
        self.forwardBtn.setToolTip(_translate("browser", "forward/前进"))
        self.refreshBtn.setToolTip(_translate("browser", "refresh page/刷新页面"))
        self.copyBtn.setToolTip(_translate("browser", "copy unfinished/复制未完成"))
        self.ensureBtn.setToolTip(_translate("browser", "download selected/下载所选"))
