# 🕑 更新历史

> [!Info] 此页面会忽略修复动作相关的记录，含引导意义的条目除外

#### v2.2.5

+ ✨jm 支持章节，仅在读剪贴板可用；去重机制对于章节同样生效  
+ 配置栏 eh_cookies 改为 cookies  
+ 将耗时操作置于预处理后台线程；将诸多耗时 io 改为异步

#### v2.2.4

+ 工具视窗增设 statusTool

#### v2.2.3

+ 工具视窗增设 domainTool；hitomiTool 也转移至其中  
+ 配置系记录系的文件转移位置  
+ hitomi 用异步并发做归并了  
+ macOS-init.app 去除，改为 bash 命令自执行  
+ mac python 改为 uv

#### v2.2.2

+ 增设 rV 按钮，工具视窗  
+ 设置储存目录防呆  

#### v2.2.0 | ~ 2025-05-20

+ 🌐支持`hitomi` （部分）
+ Kemono 脚本集更新（下载引擎使用强大的 `Motrix-PRC`）  
+ 页数命名优化：更改为纯数字补零命名，附带可选 [文件命名后缀修改](/config/#其他-yml-字段)  
+ i18n 自动编译优化  
+ 使用 astral-sh/uv 管理依赖

#### v2.1.3 | ~ 2025-04-19

+ 支持 i18n  
+ 增加贡献指南等，文档优化，并建成 github-pages 做官网

### v2.1.2 | ~ 2025-04-12

+ 更换看板娘  
+ 版面增设各网站运行状态

### v2.1.0 | ~ 2025-03-29

+ 为预览窗口各封面右上增设badge
+ 将`requirements.txt`分别以`win`,`mac_x86_64`,`mac_arm64`编译

### v2.0.0 | ~ 2025-03-21

+ `使用说明`与`更新`在`v2.0.0`后将设置在配置窗口的左下按钮，绿色包可执行程序只保留主程序（macOS加个初始化.app）  
+ 优化更新流程，贴近主流软件体验  
+ ✨使用`QFluentWidgets`优化界面与操作体验  
  + 搜索框右键选项`展开预设`, 序号输入框也有  
  + 预览窗口改造了右键菜单，增设翻页进去菜单项，附带有`CGS`内的全局快捷键  
  + 正确处理小数位级系统缩放，去掉`同步系统缩放`也有良好界面体验
（操作参考[`v1.6.3`删代码部分](#v1-6-3-2025-02-13)，后续若有反响则做成开关之类提供切换）

### v1.8.2 | ~ 2025-03-08

+ ✨预览窗口新增`复制`未完成任务按钮，配合剪贴板功能功能的流程，常用于进度卡死不动重下或补漏页

### v1.7.5 | ~ 2025-03-01

+ 序号输入扩展：输入框支持单个负数，例`-3`表示选择倒数三个

### v1.7.2 | ~ 2025-02-24

+ ✨新增`增加标识`开关勾选，为储存目录最后加上网站url上的作品id  
+ ✨细化任务：预览窗口的`子任务进度`视图  
+ 处理拷贝的隐藏漫画  
+ 修正往后jm全程不走代理（如有jm需要走代理的场景请告知开发者） 

### v1.6.3 | ~ 2025-02-13

+ ✨配置窗口新增`去重`勾选开关：分别有预览提示样式和自动过滤
+ ✨增加命令行工具(crawl_only.py)使用
+ 优化高分辨率(原开发环境为1080p)；若显示不理想可桌面右键显示设置缩放改为100%，或在[`CGS.py`](https://github.com/jasoneri/ComicGUISpider/blob/GUI/CGS.py)中删除带`setAttribute(Qt.AA_` 的两行代码

### v1.6.2 | ~ 2024-12-08

+ ✨增加域名缓存机制（针对jm/wnacg发布页访问错误），每12小时才刷新可用域名，缓存文件为`scripts/__temp/xxx_domain.txt`，可删可改
+ 处理部分用户环境无法显示ui图标相关资源问题（如对比动图/视频仍有ui图标没显示，请反馈）

### v1.6.1 | ~ 2024-11-23 
+ ✨新增读剪切板匹配生成任务功能

### v1.6.0 | ~ 2024-09-30
+ 🌐支持`Māngabz`
+ ✨支持`macOS`
+ 🌐支持`exhentai`
  + [`exhentai`]优化e绅士标题取名，优先使用副标题的中/日文作为目录名
+ ✨新增翻页功能
  + 翻页时保留选择状态
+ ✨新增预览功能
> [!Info] 内置小型浏览器，无需打开电脑浏览器，视频3有介绍各种用法

### v1.5 | 上世纪 ~ 2024-08-05
+ ✨发布相关
> [!Info] 发布开箱即用版，GUI视频使用指南

+ ✨脚本集说明(kemono,saucenao)
  + 新增`nekohouse`
+ 🌐支持`jm(禁漫)`
  + 支持车号输入
+ 🌐支持`拷贝漫画`
  + 在配置设了代理后能解锁部分漫画章节
  + 处理章节数量大于300
+ 🌐支持`wnacg`
