{"devDependencies": {"vitepress": "^1.6.3", "@vue/tsconfig": "^0.4.0", "@giscus/vue": "^3.1.1", "typescript": "4.9.5", "vue": "3.3.4"}, "scripts": {"docs:dev": "vitepress dev", "docs:build": "rm _github/README_en.md && vitepress build && mkdir -p .vitepress/dist/assets/img/icons && cp -rf assets/img/icons/website .vitepress/dist/assets/img/icons/website", "docs:preview": "vitepress preview"}, "type": "module"}