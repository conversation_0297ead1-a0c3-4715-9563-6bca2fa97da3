Vars:
  ua_accept_language: "en-US,en;q=0.9"
  hitomiDb_tmp_url: "https://github.com/jasoneri/imgur/releases/download/preset/hitomi.db"
GUI:
  DESC1: "1. First-time users, click <img src=\"%s\" height=\"25\" style=\"background-color: rgb(0, 255, 255);border-radius: 7px;\"> button. Then read `Doc(Document)` at the bottom-left corner for Config-details/GUI video guides."
  DESC2: "2. If encountering issues, check the <a href='https://jasoneri.github.io/ComicGUISpider/faq/'>📖FAQ/Extra Notes</a> document before seeking help."
  DESC_ELSE: "For issues/feature suggestions, contact with an issue."
  
  BrowserWindow_ensure_warning: "Return to selection page and ensure valid selection before proceeding."
  jm_desc: "Supports multiple book IDs (decimal numbers), e.g. `123456,654321,114514` (comma-separated)<br>[Clipboard function: Avoid copying `18comic.vip` domain due to 5-second shield - direct ID input recommended]"
  wnacg_desc: "wnacg CN source may be slow/unstable. Common network errors [Errno 11001 10054 10060]/`ReadTimeout`:<br>Restart usually resolves. If persistent after multiple attempts, contact group/submit issue."
  mangabz_desc: "Māngabz uses iPhone web version. Note: Volumes/chapters both use numeric labels (e.g. Vol.1 and Ch.1 both labeled '1') - identify via adjacent chapters."
  hitomi_desc: "Except presets, use `hitomi-tools` for input<a href='https://jsd.vxo.im/gh/jasoneri/imgur@main/CGS/hitomi-tools-usage.gif'>📹References</a><br>1. Avoid using search-keyword 2. Hitomi does not support mappings"
  check_ehetai: "Checking current environment's access to `exhentai`..."
  check_mangabz: "Checking current environment's access to `Māngabz`..."
  check_hitomi: "Checking current environment's access to `hitomi`..."
  checkisopen_text_change: "(Click)Immediately open sv-path"
  checkisopen_status_tip: "sv-path auto-open after completion"
  ACCESS_FAIL: "Current proxy configuration/global proxy environment cannot access<br>Please verify site accessibility (direct CN access unsupported)"
  cookies_copy_err: "Format error - review latest GIF instructions and retry"
  copied_tip: "Background will copy [%s] items to clipboard"
  textbrowser_load_if_http: "<b><font size=\"5\" color=\"black\">Click `Preview` button</font></b><font color=\"black\"> or </font><a href=\"%s\" ><b style=\"font-size:20px;\">View in Browser</b></a>"
  WorkThread_finish_flag: "Background process completed"
  WorkThread_empty_flag: "Background exited normally without"
  copymaga_tips: "Copy unlocked chapters (e.g. Frieren)"
  copymaga_page_status_tip: "Page offset: Each page contains 30 items. For page 3, enter 60 (outputs 60-89), etc."
  global_err_hook: "Last operation caused GUI exception - check GUI logs for details"
  input_format_err: "Input format error - hover over field for rules"
  reboot_tip: "♻️ rebooting: cleaning background processes..."
  reboot_tip2: "⛺️ rebooting: reconstructing UI and instances.."
  SearchInputStatusTip:
    manga_copy: (1)Enter `Keyword` show results. (2)Enter Space show Preset-Completer
    jm: (1)Enter `Keyword` show results. (2)Enter Space show Preset-Completer
    wnacg: (1)Enter `Keyword` show results. (2)Enter Space show Preset-Completer
    ehentai: (1)Enter `Keyword` show results. (2)Enter Space show Preset-Completer
    mangabz: (1)Enter `Keyword` show results. (2)Enter Space show Preset-Completer
    hitomi: Temp only supports using presets or `hitomi-tools` generated words
  Clip:
    process_warning: "Search process already started - requires restart to use this feature"
    db_not_found_guide: "Clipboard db not found - check related guides"
    match_none: "No tasks matched. Copy first before running this function. Current matching rule: %s"
    get_info_error: "get info fail"
    partial_fail: "Partial failure (remaining tasks in the Tasks-Dialog can still be processed)"
    all_fail: "Complete failure. Update proxy/cookies config and retry. Report an issue if persistent"
    view_log: "View detailed error stack in log file"
  Tools:
    rv_scriptp_desc: "rV Script Path"
    rv_scriptp_desc_tip: "Select Script File ( %s )"
    rv_deployBtn: "Select Directory and Deploy"
    rv_deployDesc: "You need to deploy rV. <br>1. Select an empty directory unrelated to CGS and storage<br>2. Follow console instructions, then reselect script after deployment"
    rv_deployWinRequire: "<br>3. Windows additional: Control Panel > Clock and Region > Region > Change system locale > Check Beta: Use Unicode UTF-8 > Restart"
    rv_book_marked: "Records"
    rv_merge_move: "Consolidate then Move"
    rv_combined_tip:  "Make %s Chapters merged and move to [%s]"
    rv_set_script_err: "set rV script failed，read how to deploy rV by right-button"
    hitomi_tip_search: "search selected tags in website"
    hitomi_tip_sv: "save to custom presets"
    hitomi_tip_send: "send to searchInput box"
    hitomi_tip_remove: "unuse tags, force using orderby"
    hitomi_tip_orderby: "[date:added] equals index when no tags, equals default order when tags"
    hitomi_info_copied: "Copied to clipboard, pay attention as urldecoded"
    hitomi_info_sved: "value-of-urlencode saved to head of presets"
    hitomi_info_sended: "sent to searchInput box, CGS is most compatible with urlencoded values, don't worry"
    domain_btn: "Release Page"
    domain_desc: "Click the release page and copy the usable part (mainland) as shown,<br>then click execute button. CGS will take a few seconds to check availability and notify accordingly,<br>CGS cannot bypass your network environment - if browser can't access it, CGS can't either, please note"
    doamin_success_tip: "[%s] has been set in the temporary file below, valid for 48 hours<br>[%s]<br>CGS will restart in 5 seconds to ensure background processes apply the valid domain"
    doamin_error_tip: "All domains are invalid, please check yourself. Again, CGS cannot bypass the current network environment to access"
    status_desc: "Function 1: Display website availability status. If network unstable, click CGS to official site for status;\nFunction 2: Button group updates decryption-related changes. Successful updates will restart (avoid clicking before status appears)"
    status_fetching: "Fetching website availability status"
    status_waiting: "Please wait a moment~~"
    status_web_erratic: "[Warn] Network unstable - unable to fetch all status.."
    status_cf_error: "CN access to cloudflare dev domain abnormal, visit official site for status"
    status_no_update: "Local content matches online version"
    reboot_tip: "CGS will restart in %s seconds to ensure background processes apply valid changes"
  Uic:
    chooseBoxDefault: "Select Website"
    searchinputPlaceholderText: "Input Keyword"
    chooseinputPlaceholderText: "Input Serials"
    next_btnDefaultText: "Search"
    checkisopenDefaultText: "(Auto) Open sv-path when done"
    chooseinputTip: "Examples: 0 → Select all | 2 → Single select 2 | 7+9 → Multi select 7,9 (plus) | 3-5 → Multi select 3,4,5 (hyphen) | 1+7-9 → Combined: 1,7,8,9 | -3 → Last 3"
    chooseBoxToolTip: "Check status bar for input hints after selecting a website"
    previewBtnStatusTip: "Click to open preview window (available after book list appears)"
    progressBarStatusTip: " >>> Ⅰ. Green 100% = Complete Ⅱ. Even at 100%, some pages may be missing. Use Copy Unfinished and Clip-function to fill gaps"
    sv_path_desc: "Save Path"
    sv_path_desc_tip: "Select directory"
    menu_show_completer: "Show presets"
    menu_next_page: "Next page"
    menu_prev_page: "Previous page"
    confDia_labelLogLevel: "LogLevel"
    confDia_labelDedup: "Dedup"
    confDia_labelAddUuid: "AddUuid"
    confDia_labelProxy: "Proxy"
    confDia_labelMap: "Mapping"
    confDia_labelPreset: "Preset"
    confDia_labelClipDb: "ClipDB"
    confDia_labelClipNum: "ClipEntries"
    confDia_labelConcurrNum: "ConcurrNum"
    confDia_svPathWarning: "set Save Path failed, read rule by right-button"
    confDia_descBtn: "Doc"
    confDia_updateBtn: "Update"
    confDia_updateDialog_stable: "New stable version available"
    confDia_updateDialog_dev: "New dev version available"
    confDia_supportBtn: "Support"
    confDia_promote_title: "Recommended VPN"
    confDia_promote_content: "need VPN?"
    confDia_promote_url: "https://hxlm.io/#/register?code=sZWBMyum"
    confDia_support_content: "Donations will use to buy domain etc.."
    confDia_cookies_placeholder: "Refer to the animated guide in the configuration documentation for cookie acquisition methods\nIncluding fields:\n"

EHentai:
  COOKIES_NOT_SET: "`eh-cookies` must be set for exhentai access"
  ACCESS_FAIL: "Current `eh-cookies`/proxy configuration cannot access exhentai<br>Verify site accessibility (direct CN access unsupported)"
  GUIDE: "exhentai (EH) Usage Guide:<br>1. Ensure account with `exhentai.org` access<br> - Configure `eh-cookies` per setup instructions<br>2. (CN Users) Ensure working proxy (no direct access)<br> - Use global proxy or configure proxy before launch (recommend v2rayN)<br>⚠️ API test uses 3s timeout to prevent UI freeze - check `GUI.log` for debug info if errors persist"
  JUMP_TIP: "EH pagination is special - page jump feature currently disabled"
  MAPPINGS_INDEX: "index"
  MAPPINGS_POPULAR: "popular"

SPIDER:
  SayToGui:
    exp_txt: "Enter Selected Serials in `Input Serials` field"
    exp_turn_page: "Use pagination buttons (prev/next/jump) on right of search field"
    exp_preview: "Multi-select via cover clicks in preview-window. Confirm selection combines with manual input"
    exp_replace_keyword: "Enter Selected Serials in "
    TextBrowser_error: "Selection error at {1} step: {0}<br> {2}"
    frame_book_print_extra: " →_→ Hover over number field for input rules"
    frame_book_print_retry_tip: "No results found. Possible causes:<br>1. Server blocking 2. End of results 3. Verify URL in browser"
    frame_section_print_extra: " ←_← Click【Start Crawling!】"
  chooseInput_flag: "Enter Selected Serials"
  sectionInput_flag: "Start Crawling"
  search_url_head_NotImplementedError: "Custom search URL required"
  choice_list_before_turn_page: "Previous page selections pending"
  parse_step: "Comic"
  parse_sec_step: "Chapter"
  parse_sec_not_match: "No matches found"
  parse_sec_selected: "Selected numbers"
  parse_sec_now_start_crawl_desc: "Starting crawl for《%s》chapters"
  page_less_than_one: "Invalid page number <1 - reset to first page"
  finished_success: "~~~Completed [%s] image tasks ヾ(￣▽￣ )Bye~"
  finished_err: "~~~…(￣┰￣*)………Background exited with error:<br>%s"
  finished_empty: "~~~…(￣┰￣*)………Background exited normally without image tasks - check inputs"
  close_backend_error: "~~~…(￣┰￣*)………Background process crashed - troubleshooting:"
  close_check_log_guide1: "1. Check log file for developer-defined errors or retryable network issues"
  close_check_log_guide2: "2. For non-network issues: Restart > Adjust config > Set log level to DEBUG > Reproduce error"
  close_check_log_guide3: "3. If same error persists, submit issue"
  ERO_BOOK_FOLDER: "Doujinshi"
  PUBLISH_INVALID: "<a href=\"%s\">Release page</a> inaccessible<br>Refer to📒Extra Notes Section 2 for [%s] resolution"
  DOMAINS_INVALID: "<a href=\"%s\">Release page</a> domains %s inaccessible<br>Refer to📒Extra Notes Section 2 for [%s] resolution or use domainTool"

Updater:
  ver_check: "Checking version"
  ver_file_not_exist: "Version file missing - initializing"
  check_refresh_code: "Checking code updates"
  code_downloading: "Downloading code files"
  finish: "Update complete"
  not_pkg_markdown: "Feature unavailable - requires green installation package"
  token_invalid_notification: "[Local tokens invalid/missing - using stateless GitHub API (60 req/hour limit)]\nTokens will auto-download next update"
  latest_code_overwriting: "Overwriting with latest code"
  too_much_waiting_update: "Multiple pending updates detected - skipping to latest version"
  refreshing_code: "Updating code"
  refresh_fail_retry: "Update failed - retrying"
  refresh_fail_retry_over_limit: "Network issue - retry. If persists, submit issue"
  code_is_latest: "Code already up-to-date"
  env_is_latest: "Environment up-to-date"
  ver_local_latest: "Local version current"
  ver_check_fail: "Version check failed - retry or check releases"
  update_ensure: "Confirm Update"
  updated_success: "Update successful - CGS restarts in 5s"
  doing: "Involves code and dependency updates, Wait a moment.."
  updated_fail: "❌ Update failed - rollback complete\nRetry later or download manually (CGS restarts in 10s)\nError log: [%s]"
