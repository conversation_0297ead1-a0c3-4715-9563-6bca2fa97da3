# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'D:\code\ComicGUISpider\GUI\uic\conf_dia.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets
from qfluentwidgets import CheckBox, ComboBox, CompactSpinBox, LineEdit, PrimaryToolButton, StrongBodyLabel, TextBrowser, TextEdit, TransparentToolButton


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(500, 390)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Dialog.sizePolicy().hasHeightForWidth())
        Dialog.setSizePolicy(sizePolicy)
        Dialog.setMinimumSize(QtCore.QSize(500, 470))
        Dialog.setMaximumSize(QtCore.QSize(650, 530))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/CGS-logo.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        Dialog.setWindowIcon(icon)
        self.gridLayout_2 = QtWidgets.QGridLayout(Dialog)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.dialogVLayout = QtWidgets.QVBoxLayout()
        self.dialogVLayout.setSpacing(4)
        self.dialogVLayout.setObjectName("dialogVLayout")
        self.sv_path_Layout = QtWidgets.QHBoxLayout()
        self.sv_path_Layout.setObjectName("sv_path_Layout")
        self.dialogVLayout.addLayout(self.sv_path_Layout)
        self.horizontalLayout_log_level = QtWidgets.QHBoxLayout()
        self.horizontalLayout_log_level.setObjectName("horizontalLayout_log_level")
        self.label_2 = StrongBodyLabel(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(0, 20))
        self.label_2.setMaximumSize(QtCore.QSize(60, 20))
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_log_level.addWidget(self.label_2)
        self.logLevelComboBox = ComboBox(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.logLevelComboBox.sizePolicy().hasHeightForWidth())
        self.logLevelComboBox.setSizePolicy(sizePolicy)
        self.logLevelComboBox.setObjectName("logLevelComboBox")
        self.logLevelComboBox.addItem("")
        self.logLevelComboBox.addItem("")
        self.logLevelComboBox.addItem("")
        self.logLevelComboBox.addItem("")
        self.horizontalLayout_log_level.addWidget(self.logLevelComboBox)
        self.concurr_numLabel = StrongBodyLabel(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.concurr_numLabel.sizePolicy().hasHeightForWidth())
        self.concurr_numLabel.setSizePolicy(sizePolicy)
        self.concurr_numLabel.setObjectName("concurr_numLabel")
        self.horizontalLayout_log_level.addWidget(self.concurr_numLabel)
        self.concurr_numEdit = CompactSpinBox(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.concurr_numEdit.sizePolicy().hasHeightForWidth())
        self.concurr_numEdit.setSizePolicy(sizePolicy)
        self.concurr_numEdit.setMinimum(8)
        self.concurr_numEdit.setMaximum(64)
        self.concurr_numEdit.setProperty("value", 16)
        self.concurr_numEdit.setObjectName("concurr_numEdit")
        self.horizontalLayout_log_level.addWidget(self.concurr_numEdit)
        self.line = QtWidgets.QFrame(Dialog)
        self.line.setFrameShape(QtWidgets.QFrame.VLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.horizontalLayout_log_level.addWidget(self.line)
        self.isDeduplicate = CheckBox(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.isDeduplicate.sizePolicy().hasHeightForWidth())
        self.isDeduplicate.setSizePolicy(sizePolicy)
        self.isDeduplicate.setObjectName("isDeduplicate")
        self.horizontalLayout_log_level.addWidget(self.isDeduplicate)
        self.addUuid = CheckBox(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.addUuid.sizePolicy().hasHeightForWidth())
        self.addUuid.setSizePolicy(sizePolicy)
        self.addUuid.setObjectName("addUuid")
        self.horizontalLayout_log_level.addWidget(self.addUuid)
        self.dialogVLayout.addLayout(self.horizontalLayout_log_level)
        self.horizontalLayout_proxies = QtWidgets.QHBoxLayout()
        self.horizontalLayout_proxies.setObjectName("horizontalLayout_proxies")
        self.label_4 = StrongBodyLabel(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setMinimumSize(QtCore.QSize(60, 20))
        self.label_4.setMaximumSize(QtCore.QSize(60, 20))
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_proxies.addWidget(self.label_4)
        self.proxiesEdit = LineEdit(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.proxiesEdit.sizePolicy().hasHeightForWidth())
        self.proxiesEdit.setSizePolicy(sizePolicy)
        self.proxiesEdit.setMaximumSize(QtCore.QSize(16777215, 20))
        self.proxiesEdit.setObjectName("proxiesEdit")
        self.horizontalLayout_proxies.addWidget(self.proxiesEdit)
        self.dialogVLayout.addLayout(self.horizontalLayout_proxies)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSizeConstraint(QtWidgets.QLayout.SetMaximumSize)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label_3 = StrongBodyLabel(Dialog)
        self.label_3.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setMinimumSize(QtCore.QSize(60, 20))
        self.label_3.setMaximumSize(QtCore.QSize(60, 20))
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.verticalLayout.addWidget(self.label_3)
        spacerItem = QtWidgets.QSpacerItem(20, 50, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)
        self.horizontalLayout.addLayout(self.verticalLayout)
        self.custom_mapEdit = TextEdit(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.custom_mapEdit.sizePolicy().hasHeightForWidth())
        self.custom_mapEdit.setSizePolicy(sizePolicy)
        self.custom_mapEdit.setStyleSheet("QTextEdit {\n"
"background-image: url(:/configDialog/ba_gamer.png);\n"
"color: black;\n"
"}")
        self.custom_mapEdit.setObjectName("custom_mapEdit")
        self.horizontalLayout.addWidget(self.custom_mapEdit)
        self.dialogVLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_label_completer = QtWidgets.QHBoxLayout()
        self.horizontalLayout_label_completer.setObjectName("horizontalLayout_label_completer")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label_completer = StrongBodyLabel(Dialog)
        self.label_completer.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_completer.sizePolicy().hasHeightForWidth())
        self.label_completer.setSizePolicy(sizePolicy)
        self.label_completer.setMinimumSize(QtCore.QSize(60, 20))
        self.label_completer.setMaximumSize(QtCore.QSize(60, 20))
        self.label_completer.setAlignment(QtCore.Qt.AlignCenter)
        self.label_completer.setObjectName("label_completer")
        self.verticalLayout_2.addWidget(self.label_completer)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem1)
        self.horizontalLayout_label_completer.addLayout(self.verticalLayout_2)
        self.completerEdit = TextEdit(Dialog)
        self.completerEdit.setToolTipDuration(-1)
        self.completerEdit.setStyleSheet("QTextEdit {\n"
"background-image: url(:/configDialog/ba_swimsuit.png);\n"
"color: black;\n"
"}")
        self.completerEdit.setObjectName("completerEdit")
        self.horizontalLayout_label_completer.addWidget(self.completerEdit)
        self.dialogVLayout.addLayout(self.horizontalLayout_label_completer)
        self.horizontalLayout_label_ehCookies = QtWidgets.QHBoxLayout()
        self.horizontalLayout_label_ehCookies.setObjectName("horizontalLayout_label_ehCookies")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.cookiesLabel = StrongBodyLabel(Dialog)
        self.cookiesLabel.setEnabled(True)
        self.cookiesLabel.setMinimumSize(QtCore.QSize(60, 20))
        self.cookiesLabel.setMaximumSize(QtCore.QSize(60, 40))
        self.cookiesLabel.setToolTip("")
        self.cookiesLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.cookiesLabel.setObjectName("cookiesLabel")
        self.verticalLayout_3.addWidget(self.cookiesLabel)
        self.cookiesBox = ComboBox(Dialog)
        self.cookiesBox.setObjectName("cookiesBox")
        self.verticalLayout_3.addWidget(self.cookiesBox)
        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem2)
        self.horizontalLayout_label_ehCookies.addLayout(self.verticalLayout_3)
        self.cookiesEdit = TextEdit(Dialog)
        self.cookiesEdit.setToolTip("")
        self.cookiesEdit.setToolTipDuration(-1)
        self.cookiesEdit.setStyleSheet("QTextEdit {\n"
"background-image: url(:/configDialog/fgo_bg.png);\n"
"color: black;\n"
"}")
        self.cookiesEdit.setObjectName("cookiesEdit")
        self.horizontalLayout_label_ehCookies.addWidget(self.cookiesEdit)
        self.dialogVLayout.addLayout(self.horizontalLayout_label_ehCookies)
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.clip_read_numEdit = CompactSpinBox(Dialog)
        self.clip_read_numEdit.setMinimum(1)
        self.clip_read_numEdit.setMaximum(200)
        self.clip_read_numEdit.setObjectName("clip_read_numEdit")
        self.gridLayout.addWidget(self.clip_read_numEdit, 0, 3, 1, 1)
        self.label_6 = StrongBodyLabel(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        self.label_6.setMinimumSize(QtCore.QSize(60, 0))
        self.label_6.setMaximumSize(QtCore.QSize(60, 16777215))
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 0, 0, 1, 1)
        self.clip_dbEdit = LineEdit(Dialog)
        self.clip_dbEdit.setObjectName("clip_dbEdit")
        self.gridLayout.addWidget(self.clip_dbEdit, 0, 1, 1, 1)
        self.label_7 = StrongBodyLabel(Dialog)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 0, 2, 1, 1)
        self.dialogVLayout.addLayout(self.gridLayout)
        self.bottom_btn_horizontalLayout = QtWidgets.QHBoxLayout()
        self.bottom_btn_horizontalLayout.setObjectName("bottom_btn_horizontalLayout")
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.bottom_btn_horizontalLayout.addItem(spacerItem3)
        self.acceptBtn = PrimaryToolButton(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.acceptBtn.sizePolicy().hasHeightForWidth())
        self.acceptBtn.setSizePolicy(sizePolicy)
        self.acceptBtn.setMaximumSize(QtCore.QSize(40, 16777215))
        self.acceptBtn.setObjectName("acceptBtn")
        self.bottom_btn_horizontalLayout.addWidget(self.acceptBtn)
        self.cancelBtn = TransparentToolButton(Dialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.cancelBtn.sizePolicy().hasHeightForWidth())
        self.cancelBtn.setSizePolicy(sizePolicy)
        self.cancelBtn.setMaximumSize(QtCore.QSize(40, 16777215))
        self.cancelBtn.setObjectName("cancelBtn")
        self.bottom_btn_horizontalLayout.addWidget(self.cancelBtn)
        self.dialogVLayout.addLayout(self.bottom_btn_horizontalLayout)
        self.gridLayout_2.addLayout(self.dialogVLayout, 0, 0, 1, 1)

        self.retranslateUi(Dialog)
        self.cancelBtn.clicked.connect(Dialog.reject) # type: ignore
        self.acceptBtn.clicked.connect(Dialog.accept) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "config/配置"))
        self.label_2.setToolTip(_translate("Dialog", "log_level"))
        self.logLevelComboBox.setItemText(0, _translate("Dialog", "WARNING"))
        self.logLevelComboBox.setItemText(1, _translate("Dialog", "DEBUG"))
        self.logLevelComboBox.setItemText(2, _translate("Dialog", "INFO"))
        self.logLevelComboBox.setItemText(3, _translate("Dialog", "ERROR"))
        self.label_4.setToolTip(_translate("Dialog", "proxies"))
        self.proxiesEdit.setToolTip(_translate("Dialog", "proxies"))
        self.proxiesEdit.setPlaceholderText(_translate("Dialog", "example-of-v2rayN 127.0.0.1:10809"))
        self.label_3.setToolTip(_translate("Dialog", "custom_map"))
        self.label_completer.setToolTip(_translate("Dialog", "completer/preset"))
        self.completerEdit.setToolTip(_translate("Dialog", "completer/preset"))
        self.cookiesLabel.setText(_translate("Dialog", "Cookies"))
