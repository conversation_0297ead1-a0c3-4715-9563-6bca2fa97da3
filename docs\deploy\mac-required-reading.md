# 💻 macOS( mac 操作系统) 部署

## 📑 说明

::: tip v2.3.3-beta 之后的绿色包均转为套壳的 `uv tool`
绿色包 `CGS-macOS.7z` 里 `CGS.app` 其执行内容为

```bash
if [ ! -x "cgs" ]; then
    curl -fsSL https://gitee.com/json_eri/ComicGUISpider/raw/GUI/deploy/launcher/mac/init.bash | bash && cgs
else
    cgs
fi
```

---

也推荐干脆直接使用 [uv tool方式部署安装](/deploy/quick-start#1-下载--部署)
:::



## ⛵️ 操作

::: warning 源码根目录
```bash

```
:::
::: warning macOS 由于认证签名收费，app 初次打开可能会有限制，正确操作如下

1. 对 app 右键打开，报错不要丢垃圾篓，直接取消
2. 再对 app 右键打开，此时弹出窗口有打开选项，能打开了
3. 后续就能双击打开，不用右键打开了
:::
::: warning 以下初始化步骤严格按序执行
:::

|   初次化步骤    | 解析说明                                                                                                                                                                                                                                                                           |
|:------:|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1   | 每次解压后，将`CGS.app`移至应用程序<br/> ![图示](../assets/img/deploy/mac-app-move.jpg)|
| 1.5   | （可选，需要在第2步前进行）由于macOS没微软雅黑字体，默认替换成`冬青黑体简体中文`<br/>不清楚是否每种macOS必有，留了后门替换，在 `scripts/deploy/launcher/mac/__init__.py` 的`font`值，有注释说明 |
| 2   | 打开终端执行 初始化/环境更新 命令，（牢记此页面有这条命令，经常用）<br>`bash /Applications/CGS.app/Contents/Resources/scripts/deploy/launcher/mac/init.bash`<br/>⚠️ _**根据提示操作**_ （对应第1.5步改字体可以反复执行第2步） |

## 🔰 其他

### 针对弹窗报错的尝试

```bash
# CGS.app显示损坏无法打开时，尝试绕过签名 
sudo xattr -d com.apple.quarantine /Applications/CGS.app
# 或
sudo xattr -r -d com.apple.quarantine /Applications/CGS.app

# 或直接运行
bash /Applications/CGS.app/Contents/Resources/scripts/deploy/launcher/mac/CGS.bash
```

::: tip 还是失败无果的情况下可先自行deepseek等寻找方法或群内反馈  
除命令以外能成功`解决CGS.app显示损坏`的示例请在下方评论区留言，造福后人  
格式：1.报错信息;2.解决方案;3.结果
:::

### bug report / 提交报错 issue

macOS上运行软件出错需要提issue时，除系统选`macOS`外，还需描述加上系统版本与架构  
（开发者测试开发环境为`macOS Sonoma(14) / x86_64`）
