# -*- coding: utf-8 -*-

# Scrapy settings for ComicSpider project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html
import warnings
from scrapy.exceptions import ScrapyDeprecationWarning

from variables import SPECIAL_WEBSITES
from utils import conf

warnings.filterwarnings("ignore", message=".*deprecated start_requests.*", category=ScrapyDeprecationWarning)

BOT_NAME = 'ComicSpider'

SPIDER_MODULES = ['ComicSpider.spiders']
NEWSPIDER_MODULE = 'ComicSpider.spiders'


# Crawl responsibly by identifying yourself (and your website) on the user-agent
#USER_AGENT = 'ComicSpider (+http://www.yourdomain.com)'

# Obey robots.txt rules
ROBOTSTXT_OBEY = False
# Configure maximum concurrent requests performed by Scrapy (default: 16)

# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
DOWNLOAD_DELAY = 0.5
# The download delay setting will honor only one of:
#CONCURRENT_REQUESTS_PER_DOMAIN = 16
#CONCURRENT_REQUESTS_PER_IP = 16

# Override the default request headers:
DEFAULT_REQUEST_HEADERS = {
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'en',
}

SPIDER_MIDDLEWARES = {
   'ComicSpider.middlewares.GlobalErrorHandlerMiddleware': 100,
}

DOWNLOADER_MIDDLEWARES = {
   'ComicSpider.middlewares.ComicspiderDownloaderMiddleware': 5,
}

ITEM_PIPELINES = {
   'ComicSpider.pipelines.ComicPipeline': 50
}

IMAGES_STORE = '/'
SV_PATH, log_path, PROXY_CUST, LOG_LEVEL, CUSTOM_MAP, CONCURR_NUM = conf.settings

CONCURRENT_REQUESTS = int(CONCURR_NUM)
UA = [r"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:70.0) Gecko/20100101 Firefox/101.0",
      r'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.0',
      ]

# 日志输出
LOG_FILE = log_path.joinpath("scrapy.log")
SPECIAL = SPECIAL_WEBSITES
