---
title: 简介
---


<p align="center">
  <picture>
    <img src="../public/CGS-girl.png" width=50%>
  </picture>
  <a href="https://git.io/typing-svg"><img src="https://readme-typing-svg.demolab.com?font=Fira+Code&weight=700&size=40&duration=3000&pause=10000&color=D39620&center=true&vCenter=true&repeat=false&width=500&lines=ComicGUISpider(CGS)" alt="Typing SVG" /></a>
</p>

## 📑 简介

**`CGS`** 是一个... 能简单使用的漫画下载软件！（忽略200字说明）

## 功能说明

- 简易配置就能使用
- 开预览后随便点点就能下载，还能基于预览窗口进网站看
- 通过加减号，`0 全选`，`-3 选倒数三个` 等输入规则，能方便指定选择
- 基于翻页保留，翻页就像已塞进了购物车一样
- 虽然任务是顺序流，但内置重启很方便，加上多开更方便
- 读剪贴板方式流，字如其名
- 去重，加标识符等

## 致谢声明

### Credits

Thanks to 
- [PyStand](https://github.com/skywind3000/PyStand) / [Platypus](https://github.com/sveinbjornt/Platypus) for providing win/macOS packaging.
- [Ditto](https://github.com/sabrogden/Ditto) / [Maccy](https://github.com/p0deje/Maccy) for providing great win/macOS Clipboard Soft.
- [PyQt-Fluent-Widgets](https://github.com/zhiyiYo/PyQt-Fluent-Widgets/) for providing elegant qfluent ui.
- [VitePress](https://vitepress.dev) for providing a great documentation framework.
- Every comic production team / translator team / fans.

## 贡献

欢迎提供 ISSUE 或者 PR

<a href="https://github.com/jasoneri/ComicGUISpider/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=jasoneri/ComicGUISpider" />
</a>

## 传播声明

- **请勿**将 ComicGUISpider 用于商业用途。
- **请勿**将 ComicGUISpider 制作为视频内容，于境内视频网站(版权利益方)传播。
- **请勿**将 ComicGUISpider 用于任何违反法律法规的行为。

ComicGUISpider 仅供学习交流使用。

## Licence

[MIT licence](https://github.com/jasoneri/ComicGUISpider/blob/GUI/LICENSE)
