name: Delete workflow runs

on:
  schedule:
    - cron: '0 5 */4 * *'
  workflow_dispatch:

jobs:
  changes:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      contents: read

    steps:
      - name: Delete workflow runs
        uses: Mattraks/delete-workflow-runs@main
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          retain_days: 0
          keep_minimum_runs: 10
