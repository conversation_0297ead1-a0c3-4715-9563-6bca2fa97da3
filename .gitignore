# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

##----------idea----------
*.iml
.idea/
*.ipr
*.iws

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
node_modules/
docs/.vitepress/dist
docs/.vitepress/cache
package-lock.json

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# GUI related
*.ui
source/

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.po
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

##----------Other----------
# osx
*~
.DS_Store
gradle.properties
comic/

# Package Files #
*.jar
*.war
*.nar
*.ear
====.zip
*.tar.gz
====.rar
====.exe
*.xml

##----------Python----------
*_origin.py
setting.txt
*.pyc
*_info.txt
private_*.json
*test*.py

# Sphinx documentation
docs/_build/
log/

# PyBuilder
target/

# vscode
.vscode

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# self
analyze/
*.d
*.zip
*.psd
*demo*.py
__temp
test/*
.lh/
assets/res/*.png
custom/bg/*
custom/start/*
# self/build
*.ico
*.qrc
src/
!GUI/src/preview_format/
!GUI/src/material_ct.py
!GUI/src/__init__.py
temp
*-in.txt
untitled*
Pipfile
Pipfile.lock
codecov*
*_local.yml
*-lock.yaml
*.hash
uv.lock
# self/conf
gitee_t.json
conf*.yml
record.db
hitomi.db
# self/desc created html
desc.html
docs/*.html
deploy/launcher/mac/*.html
# self/bug-report
_bug_log
# ide
.cursor